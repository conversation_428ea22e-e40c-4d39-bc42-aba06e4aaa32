package com.data.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.data.common.core.domain.model.LoginUser;
import com.data.common.core.page.TableDataInfo;
import com.data.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.data.common.utils.DateUtils;
import com.data.common.utils.StringUtils;
import com.data.common.exception.ServiceException;
import com.data.system.domain.Sm10SpecialSuppleMap;
import com.data.system.domain.bo.DdOblistSuppleBo;
import com.data.system.domain.vo.DdOblistSuppleVo;
import com.data.system.mapper.Sm10SpecialSuppleMapMapper;
import com.data.system.service.IDdOblistSuppleService;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.data.system.domain.bo.Sm10SpecialConfBo;
import com.data.system.domain.vo.Sm10SpecialConfVo;
import com.data.system.domain.Sm10SpecialConf;
import com.data.system.mapper.Sm10SpecialConfMapper;
import com.data.system.service.ISm10SpecialConfService;

import java.util.ArrayList;
import java.util.List;
import java.util.Collection;

/**
 * 补录专题配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-01
 */
@RequiredArgsConstructor
@Service
public class Sm10SpecialConfServiceImpl implements ISm10SpecialConfService {

    private final IDdOblistSuppleService iDdOblistSuppleService;

    private final Sm10SpecialConfMapper baseMapper;

    private final Sm10SpecialSuppleMapMapper sm10SpecialSuppleMapMapper;

    /**
     * 查询补录专题配置
     */
    @Override
    public Sm10SpecialConfVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询补录专题配置列表
     */
    @Override
    public TableDataInfo<Sm10SpecialConfVo> queryPageList(Sm10SpecialConfBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Sm10SpecialConf> lqw = buildQueryWrapper(bo);
        Page<Sm10SpecialConfVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询补录专题配置列表
     */
    @Override
    public List<Sm10SpecialConfVo> queryList(Sm10SpecialConfBo bo) {
        LambdaQueryWrapper<Sm10SpecialConf> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Sm10SpecialConf> buildQueryWrapper(Sm10SpecialConfBo bo) {
        LambdaQueryWrapper<Sm10SpecialConf> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getSpecialName()), Sm10SpecialConf::getSpecialName, bo.getSpecialName());
        lqw.eq(bo.getSortNum() != null, Sm10SpecialConf::getSortNum, bo.getSortNum());
        lqw.eq(StringUtils.isNotBlank(bo.getSpecialStatus()), Sm10SpecialConf::getSpecialStatus, bo.getSpecialStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getQueryCondType()), Sm10SpecialConf::getQueryCondType, bo.getQueryCondType());
        lqw.eq(StringUtils.isNotBlank(bo.getQueryCondition()), Sm10SpecialConf::getQueryCondition, bo.getQueryCondition());
        lqw.eq(StringUtils.isNotBlank(bo.getDataStatus()), Sm10SpecialConf::getDataStatus, bo.getDataStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateDate()), Sm10SpecialConf::getCreateDate, bo.getCreateDate());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateUser()), Sm10SpecialConf::getCreateUser, bo.getCreateUser());
        lqw.eq(StringUtils.isNotBlank(bo.getModifyDate()), Sm10SpecialConf::getModifyDate, bo.getModifyDate());
        lqw.eq(StringUtils.isNotBlank(bo.getModifyUser()), Sm10SpecialConf::getModifyUser, bo.getModifyUser());
        // 或者使用 between（如果 bo 提供了两个参数）
         if (bo.getParams().get("beginCreateTime") != null){
                lqw.between(true,Sm10SpecialConf::getCreateDate, bo.getParams().get("beginCreateTime"), bo.getParams().get("endCreateTime"));
            }
        lqw.orderByAsc(Sm10SpecialConf::getSortNum);
        return lqw;
    }

    /**
     * 新增补录专题配置
     */
    @Override
    public Boolean insertByBo(Sm10SpecialConfBo bo, LoginUser user) {
        Sm10SpecialConf add = BeanUtil.toBean(bo, Sm10SpecialConf.class);
        add.setCreateDate(DateUtils.getNowDate());
        add.setCreateUser(user.getUsername());
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            //进行专题与补录数据映射表数据插入
            DdOblistSuppleBo dbo = new DdOblistSuppleBo();
            dbo.setSpecialSup(bo.getQueryCondition());
            this.saveSpecialSuppleMap(dbo, add, user);
        }
        return flag;
    }

    /**
     * 修改补录专题配置
     */
    @Override
    public Boolean updateByBo(Sm10SpecialConfBo bo, LoginUser user) {
        Sm10SpecialConf update = BeanUtil.toBean(bo, Sm10SpecialConf.class);
        update.setModifyDate(DateUtils.getNowDate());
        update.setModifyUser(user.getUsername());
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            //删除映射表数据
            LambdaQueryWrapper<Sm10SpecialSuppleMap> lqw = Wrappers.lambdaQuery();
            lqw.eq(true, Sm10SpecialSuppleMap::getSpecialId, update.getId());
            sm10SpecialSuppleMapMapper.delete(lqw);
            //进行专题与补录数据映射表数据插入
            DdOblistSuppleBo dbo = new DdOblistSuppleBo();
            dbo.setSpecialSup(bo.getQueryCondition());
            this.saveSpecialSuppleMap(dbo, update, user);
        }
        return flag;
    }

    /**
     * 进行专题与补录数据映射表数据插入 由于数据量可能较大，才用异步方式进行删除保存，否则会导致页面超时
     * @param dbo DdOblistSuppleBo
     * @param update Sm10SpecialConf
     * @param user LoginUser
     */
    @Async
    public void saveSpecialSuppleMap(DdOblistSuppleBo dbo, Sm10SpecialConf update, LoginUser user) {
        List<DdOblistSuppleVo> ddOblistSuppleVos = iDdOblistSuppleService.queryList(dbo);
        if (ddOblistSuppleVos != null && !ddOblistSuppleVos.isEmpty()) {
            List<Sm10SpecialSuppleMap> specialSuppleMaps = new ArrayList<>();
            for (DdOblistSuppleVo item : ddOblistSuppleVos) {
                Sm10SpecialSuppleMap specialSuppleMap  = new Sm10SpecialSuppleMap();
                specialSuppleMap.setSpecialId(update.getId());
                specialSuppleMap.setObjId(item.getObjId());
                specialSuppleMap.setSuppleStatus(item.getSuppleStatus());
                specialSuppleMap.setCreateDate(DateUtils.getNowDate());
                specialSuppleMap.setCreateUser(user.getUsername());
                specialSuppleMaps.add(specialSuppleMap);
            }
            sm10SpecialSuppleMapMapper.insertBatch(specialSuppleMaps);
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Sm10SpecialConf entity){
        // 检查专题名称是否唯一
        LambdaQueryWrapper<Sm10SpecialConf> lqw = Wrappers.lambdaQuery();
        lqw.eq(Sm10SpecialConf::getSpecialName, entity.getSpecialName());

        // 如果是更新操作，排除当前记录
        if (entity.getId() != null) {
            lqw.ne(Sm10SpecialConf::getId, entity.getId());
        }

        // 查询是否存在重复的专题名称
        long count = baseMapper.selectCount(lqw);
        if (count > 0) {
            throw new ServiceException("专题名称已存在，请使用其他名称");
        }
    }

    /**
     * 批量删除补录专题配置
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
