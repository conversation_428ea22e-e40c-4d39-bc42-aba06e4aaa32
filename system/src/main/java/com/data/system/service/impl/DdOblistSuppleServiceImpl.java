package com.data.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.data.common.convert.ConverterUtils;
import com.data.common.core.domain.PageQuery;
import com.data.common.core.domain.model.LoginUser;
import com.data.common.core.page.TableDataInfo;
import com.data.common.helper.LoginHelper;
import com.data.common.utils.DateUtils;
import com.data.common.utils.JsonComparatorUtil;
import com.data.common.utils.StringUtils;
import com.data.system.domain.*;
import com.data.system.domain.bo.DdOblistSuppleBo;
import com.data.system.domain.vo.DdOblistSuppleOrigVo;
import com.data.system.domain.vo.DdOblistSuppleVo;
import com.data.system.domain.vo.Sm10EditTableLogVo;
import com.data.system.domain.vo.Sm10SpecialSuppleMapVo;
import com.data.system.mapper.*;
import com.data.system.service.IDdOblistSuppleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 关注名单主数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class DdOblistSuppleServiceImpl implements IDdOblistSuppleService {

    private final DdOblistSuppleMapper baseMapper;

    private final Sm10SpecialSuppleMapMapper sm10SpecialSuppleMapMapper;

    private final Sm10EditTableLogMapper sm10EditTableLogMapper;

    private final Sm10EditCheckLogMapper sm10EditCheckLogMapper;
    private final SysUserMapper sysUserMapper;

    private final DdOblistSuppleOrigMapper ddOblistSuppleOrigMapper;

    /**
     * 查询关注名单主数据
     */
    @Override
    public DdOblistSuppleVo queryById(String objId){
        DdOblistSuppleVo ddOblistSuppleVo = baseMapper.selectVoById(objId);
        if(ddOblistSuppleVo.getApplyId() != null){
            ddOblistSuppleVo.setApplyName(sysUserMapper.selectUserById(ddOblistSuppleVo.getApplyId()).getNickName());
        }
        if(ddOblistSuppleVo.getSuppleId() != null){
            ddOblistSuppleVo.setSuppleName(sysUserMapper.selectUserById(ddOblistSuppleVo.getSuppleId()).getNickName());
        }
        if(ddOblistSuppleVo.getSuppleStatus() != null && ddOblistSuppleVo.getSuppleStatus() == 3){
            DdOblistSuppleOrigVo ddOblistSuppleOrigVo = ddOblistSuppleOrigMapper.selectVoById(objId);
            // 计算两个对象之间的差异并存入diffList
            calculateDifferences(ddOblistSuppleVo, ddOblistSuppleOrigVo);
        }
        return ddOblistSuppleVo;
    }

    /**
     * 计算两个对象之间的差异并存入diffList
     *
     * @param ddOblistSuppleVo 补录数据
     * @param ddOblistSuppleOrigVo 原始数据
     */
    private void calculateDifferences(DdOblistSuppleVo ddOblistSuppleVo, DdOblistSuppleOrigVo ddOblistSuppleOrigVo) {
        if (ddOblistSuppleVo == null || ddOblistSuppleOrigVo == null) {
            return;
        }

        List<HashMap<String, String>> diffList = new ArrayList<>();

        // 使用反射获取所有字段
        Field[] fields = DdOblistSuppleVo.class.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            try {
                // 跳过diffList字段本身和序列化ID
                if ("diffList".equals(field.getName()) || "serialVersionUID".equals(field.getName())) {
                    continue;
                }

                // 尝试在原始对象中找到相同名称的字段
                Field origField = null;
                try {
                    origField = DdOblistSuppleOrigVo.class.getDeclaredField(field.getName());
                    origField.setAccessible(true);
                } catch (NoSuchFieldException e) {
                    // 如果原始对象中没有该字段，则跳过
                    continue;
                }

                // 获取两个对象中该字段的值
                Object suppleValue = field.get(ddOblistSuppleVo);
                Object origValue = origField.get(ddOblistSuppleOrigVo);

                // 比较两个值是否相等
                if (!Objects.equals(suppleValue, origValue)) {
                    // 处理Date类型的字段，转换为yyyy-MM-dd格式
                    String oldValueStr = "";
                    String newValueStr = "";

                    if (origValue instanceof Date) {
                        oldValueStr = origValue != null ? DateUtils.parseDateToStr("yyyy-MM-dd", (Date)origValue) : "";
                    } else {
                        oldValueStr = origValue != null ? origValue.toString() : "";
                    }

                    if (suppleValue instanceof Date) {
                        newValueStr = suppleValue != null ? DateUtils.parseDateToStr("yyyy-MM-dd", (Date)suppleValue) : "";
                    } else {
                        newValueStr = suppleValue != null ? suppleValue.toString() : "";
                    }

                    // 只有当两个值不同且不同时为空时才添加差异
                    if (!(StringUtils.isBlank(oldValueStr) && StringUtils.isBlank(newValueStr))) {
                        // 如果不相等，创建一个新的HashMap存储差异信息
                        HashMap<String, String> diffMap = new HashMap<>();

                        // 存储字段名称、原始值和新值
                        diffMap.put("fieldName", field.getName()); // 字段名称
                        diffMap.put("oldValue", oldValueStr); // 原始值
                        diffMap.put("newValue", newValueStr); // 新值

                        diffList.add(diffMap);
                    }
                }
            } catch (IllegalAccessException e) {
                // 处理异常
                log.error("处理异常: " + field.getName(), e);
            }
        }

        // 将差异列表设置到ddOblistSuppleVo对象中
        ddOblistSuppleVo.setDiffList(diffList);
    }

    /**
     * 查询关注名单主数据列表
     */
    @Override
    public TableDataInfo<DdOblistSuppleVo> queryPageList(DdOblistSuppleBo bo, PageQuery pageQuery, LoginUser user) {
        bo.setSuppleId(user.getUserId());
        LambdaQueryWrapper<DdOblistSupple> lqw = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(bo.getSpecialSup())){
            lqw.in(true, DdOblistSupple::getSuppleStatus, 1,2,9);
            lqw.apply(bo.getSpecialSup());
        }else {
            lqw.in(bo.getSuppleStatus() == null, DdOblistSupple::getSuppleStatus, 1,2,9);
            lqw.eq(bo.getSuppleStatus() != null, DdOblistSupple::getSuppleStatus, bo.getSuppleStatus());
            if(bo.getSuppleStatus() != null && bo.getSuppleStatus() == 3){
                lqw.and(wrapper -> {wrapper.eq(DdOblistSupple::getSuppleId, user.getUserId()).or().isNull(DdOblistSupple::getSuppleId);});
            }
            lqw.eq(bo.getSuppleSource() != null, DdOblistSupple::getSuppleSource, bo.getSuppleSource());
            lqw.like(StringUtils.isNotBlank(bo.getPrimaryName()), DdOblistSupple::getPrimaryName, bo.getPrimaryName());
            lqw.between(bo.getParams().get("beginCreateTime") != null, DdOblistSupple::getUpdateDate, bo.getParams().get("beginCreateTime"), bo.getParams().get("endCreateTime"));
            lqw.eq(StringUtils.isNotBlank(bo.getKeyword()), DdOblistSupple::getKeyword, bo.getKeyword());
            lqw.eq(StringUtils.isNotBlank(bo.getObjId()), DdOblistSupple::getObjId, bo.getObjId()); //增加名单编号obj_id精准查询条件
        }
        lqw.orderByAsc(DdOblistSupple::getId);
        Page<DdOblistSuppleVo> result = baseMapper.getPageSuppleList(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询关注名单主数据列表
     */
    @Override
    public List<DdOblistSuppleVo> queryList(DdOblistSuppleBo bo) {
        LambdaQueryWrapper<DdOblistSupple> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<DdOblistSupple> buildQueryWrapper(DdOblistSuppleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DdOblistSupple> lqw = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(bo.getSpecialSup())){
            lqw.in(bo.getSuppleStatus() == null, DdOblistSupple::getSuppleStatus, 1,2,9);
            lqw.apply(bo.getSpecialSup());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getListRange()), DdOblistSupple::getListRange, bo.getListRange());
        lqw.like(StringUtils.isNotBlank(bo.getListRangeName()), DdOblistSupple::getListRangeName, bo.getListRangeName());
        lqw.eq(StringUtils.isNotBlank(bo.getListType()), DdOblistSupple::getListType, bo.getListType());
        lqw.like(StringUtils.isNotBlank(bo.getListTypeName()), DdOblistSupple::getListTypeName, bo.getListTypeName());
        lqw.eq(StringUtils.isNotBlank(bo.getListSubtype()), DdOblistSupple::getListSubtype, bo.getListSubtype());
        lqw.like(StringUtils.isNotBlank(bo.getListSubtypeName()), DdOblistSupple::getListSubtypeName, bo.getListSubtypeName());
        lqw.eq(StringUtils.isNotBlank(bo.getKeyword()), DdOblistSupple::getKeyword, bo.getKeyword());
        lqw.like(StringUtils.isNotBlank(bo.getKeywordName()), DdOblistSupple::getKeywordName, bo.getKeywordName());
        lqw.eq(StringUtils.isNotBlank(bo.getListLabels()), DdOblistSupple::getListLabels, bo.getListLabels());
        lqw.eq(StringUtils.isNotBlank(bo.getListAttr()), DdOblistSupple::getListAttr, bo.getListAttr());
        lqw.eq(StringUtils.isNotBlank(bo.getSubscribeLabel()), DdOblistSupple::getSubscribeLabel, bo.getSubscribeLabel());
        lqw.eq(StringUtils.isNotBlank(bo.getConcernLevel()), DdOblistSupple::getConcernLevel, bo.getConcernLevel());
        lqw.eq(StringUtils.isNotBlank(bo.getConcernDegree()), DdOblistSupple::getConcernDegree, bo.getConcernDegree());
        lqw.eq(StringUtils.isNotBlank(bo.getActiveStatus()), DdOblistSupple::getActiveStatus, bo.getActiveStatus());
        lqw.eq(bo.getUpdateDate() != null, DdOblistSupple::getUpdateDate, bo.getUpdateDate());
        lqw.eq(StringUtils.isNotBlank(bo.getAddType()), DdOblistSupple::getAddType, bo.getAddType());
        lqw.eq(StringUtils.isNotBlank(bo.getEntityType()), DdOblistSupple::getEntityType, bo.getEntityType());
        lqw.like(StringUtils.isNotBlank(bo.getEntityTypeName()), DdOblistSupple::getEntityTypeName, bo.getEntityTypeName());
        lqw.eq(StringUtils.isNotBlank(bo.getGender()), DdOblistSupple::getGender, bo.getGender());
        lqw.eq(StringUtils.isNotBlank(bo.getBirthDate()), DdOblistSupple::getBirthDate, bo.getBirthDate());
        lqw.eq(StringUtils.isNotBlank(bo.getDeathDate()), DdOblistSupple::getDeathDate, bo.getDeathDate());
        lqw.eq(StringUtils.isNotBlank(bo.getLinkObjId()), DdOblistSupple::getLinkObjId, bo.getLinkObjId());
        lqw.eq(StringUtils.isNotBlank(bo.getRelationship()), DdOblistSupple::getRelationship, bo.getRelationship());
        lqw.like(StringUtils.isNotBlank(bo.getRelationshipName()), DdOblistSupple::getRelationshipName, bo.getRelationshipName());
        lqw.eq(StringUtils.isNotBlank(bo.getRelationshipDes()), DdOblistSupple::getRelationshipDes, bo.getRelationshipDes());
        lqw.eq(StringUtils.isNotBlank(bo.getRemarks()), DdOblistSupple::getRemarks, bo.getRemarks());
        lqw.eq(StringUtils.isNotBlank(bo.getListOriginalDes()), DdOblistSupple::getListOriginalDes, bo.getListOriginalDes());
        lqw.eq(StringUtils.isNotBlank(bo.getListPublishOrg()), DdOblistSupple::getListPublishOrg, bo.getListPublishOrg());
        lqw.eq(StringUtils.isNotBlank(bo.getPublishDate()), DdOblistSupple::getPublishDate, bo.getPublishDate());
        lqw.eq(StringUtils.isNotBlank(bo.getInvalidDate()), DdOblistSupple::getInvalidDate, bo.getInvalidDate());
        lqw.eq(StringUtils.isNotBlank(bo.getOriginalUrl()), DdOblistSupple::getOriginalUrl, bo.getOriginalUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getListOriginalHtml()), DdOblistSupple::getListOriginalHtml, bo.getListOriginalHtml());
        lqw.eq(StringUtils.isNotBlank(bo.getListOriginalTxt()), DdOblistSupple::getListOriginalTxt, bo.getListOriginalTxt());
        lqw.eq(StringUtils.isNotBlank(bo.getPhotoUrl()), DdOblistSupple::getPhotoUrl, bo.getPhotoUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getPhotoAttach()), DdOblistSupple::getPhotoAttach, bo.getPhotoAttach());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachUrl()), DdOblistSupple::getAttachUrl, bo.getAttachUrl());
        lqw.like(StringUtils.isNotBlank(bo.getPrimaryName()), DdOblistSupple::getPrimaryName, bo.getPrimaryName());
        lqw.like(StringUtils.isNotBlank(bo.getCnName()), DdOblistSupple::getCnName, bo.getCnName());
        lqw.like(StringUtils.isNotBlank(bo.getEnName()), DdOblistSupple::getEnName, bo.getEnName());
        lqw.like(StringUtils.isNotBlank(bo.getAliasName()), DdOblistSupple::getAliasName, bo.getAliasName());
        lqw.like(StringUtils.isNotBlank(bo.getAkaName()), DdOblistSupple::getAkaName, bo.getAkaName());
        lqw.like(StringUtils.isNotBlank(bo.getOriginalName()), DdOblistSupple::getOriginalName, bo.getOriginalName());
        lqw.like(StringUtils.isNotBlank(bo.getShortName()), DdOblistSupple::getShortName, bo.getShortName());
        lqw.like(StringUtils.isNotBlank(bo.getOtherName()), DdOblistSupple::getOtherName, bo.getOtherName());
        lqw.eq(StringUtils.isNotBlank(bo.getDocumentCountryCode()), DdOblistSupple::getDocumentCountryCode, bo.getDocumentCountryCode());
        lqw.like(StringUtils.isNotBlank(bo.getDocumentCountryName()), DdOblistSupple::getDocumentCountryName, bo.getDocumentCountryName());
        lqw.eq(StringUtils.isNotBlank(bo.getDocumentType()), DdOblistSupple::getDocumentType, bo.getDocumentType());
        lqw.like(StringUtils.isNotBlank(bo.getDocumentTypeName()), DdOblistSupple::getDocumentTypeName, bo.getDocumentTypeName());
        lqw.eq(StringUtils.isNotBlank(bo.getDocumentNumber()), DdOblistSupple::getDocumentNumber, bo.getDocumentNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getOtherIdType()), DdOblistSupple::getOtherIdType, bo.getOtherIdType());
        lqw.like(StringUtils.isNotBlank(bo.getOtherIdTypeName()), DdOblistSupple::getOtherIdTypeName, bo.getOtherIdTypeName());
        lqw.eq(StringUtils.isNotBlank(bo.getOtherId()), DdOblistSupple::getOtherId, bo.getOtherId());
        lqw.eq(StringUtils.isNotBlank(bo.getDocumentDes()), DdOblistSupple::getDocumentDes, bo.getDocumentDes());
        lqw.eq(StringUtils.isNotBlank(bo.getBirthAddr()), DdOblistSupple::getBirthAddr, bo.getBirthAddr());
        lqw.eq(StringUtils.isNotBlank(bo.getCountryCode()), DdOblistSupple::getCountryCode, bo.getCountryCode());
        lqw.like(StringUtils.isNotBlank(bo.getCountryName()), DdOblistSupple::getCountryName, bo.getCountryName());
        lqw.like(StringUtils.isNotBlank(bo.getCityName()), DdOblistSupple::getCityName, bo.getCityName());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), DdOblistSupple::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getCountryDes()), DdOblistSupple::getCountryDes, bo.getCountryDes());
        lqw.eq(StringUtils.isNotBlank(bo.getOtherAddrDes()), DdOblistSupple::getOtherAddrDes, bo.getOtherAddrDes());
        lqw.like(StringUtils.isNotBlank(bo.getTitleName()), DdOblistSupple::getTitleName, bo.getTitleName());
        lqw.eq(StringUtils.isNotBlank(bo.getOccupation()), DdOblistSupple::getOccupation, bo.getOccupation());
        lqw.like(StringUtils.isNotBlank(bo.getOccupationName()), DdOblistSupple::getOccupationName, bo.getOccupationName());
        lqw.eq(StringUtils.isNotBlank(bo.getOccupationDes()), DdOblistSupple::getOccupationDes, bo.getOccupationDes());
        lqw.like(StringUtils.isNotBlank(bo.getAscOrgName()), DdOblistSupple::getAscOrgName, bo.getAscOrgName());
        lqw.eq(StringUtils.isNotBlank(bo.getCompanyWebsite()), DdOblistSupple::getCompanyWebsite, bo.getCompanyWebsite());
        lqw.eq(StringUtils.isNotBlank(bo.getSanDes()), DdOblistSupple::getSanDes, bo.getSanDes());
        lqw.eq(StringUtils.isNotBlank(bo.getSanAct()), DdOblistSupple::getSanAct, bo.getSanAct());
        lqw.eq(StringUtils.isNotBlank(bo.getImoNum()), DdOblistSupple::getImoNum, bo.getImoNum());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrOwner()), DdOblistSupple::getCurrOwner, bo.getCurrOwner());
        lqw.eq(StringUtils.isNotBlank(bo.getPrevOwner()), DdOblistSupple::getPrevOwner, bo.getPrevOwner());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrShipFlag()), DdOblistSupple::getCurrShipFlag, bo.getCurrShipFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getPrevFlag()), DdOblistSupple::getPrevFlag, bo.getPrevFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getShipType()), DdOblistSupple::getShipType, bo.getShipType());
        lqw.eq(StringUtils.isNotBlank(bo.getShipTonnage()), DdOblistSupple::getShipTonnage, bo.getShipTonnage());
        lqw.eq(StringUtils.isNotBlank(bo.getShipLength()), DdOblistSupple::getShipLength, bo.getShipLength());
        lqw.eq(StringUtils.isNotBlank(bo.getBuiltYear()), DdOblistSupple::getBuiltYear, bo.getBuiltYear());
        lqw.eq(StringUtils.isNotBlank(bo.getHin()), DdOblistSupple::getHin, bo.getHin());
        lqw.eq(StringUtils.isNotBlank(bo.getPublishSource()), DdOblistSupple::getPublishSource, bo.getPublishSource());
        lqw.eq(StringUtils.isNotBlank(bo.getIsRecord()), DdOblistSupple::getIsRecord, bo.getIsRecord());
        lqw.eq(bo.getRecordDate() != null, DdOblistSupple::getRecordDate, bo.getRecordDate());
        lqw.eq(StringUtils.isNotBlank(bo.getRecordUser()), DdOblistSupple::getRecordUser, bo.getRecordUser());
        lqw.eq(StringUtils.isNotBlank(bo.getUpdateType()), DdOblistSupple::getUpdateType, bo.getUpdateType());
        lqw.eq(StringUtils.isNotBlank(bo.getIsUse()), DdOblistSupple::getIsUse, bo.getIsUse());
        lqw.eq(bo.getDataDate() != null, DdOblistSupple::getDataDate, bo.getDataDate());
        lqw.eq(bo.getLoadDate() != null, DdOblistSupple::getLoadDate, bo.getLoadDate());
        lqw.eq(bo.getCraeteDate() != null, DdOblistSupple::getCraeteDate, bo.getCraeteDate());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateUser()), DdOblistSupple::getCreateUser, bo.getCreateUser());
        lqw.eq(bo.getModifyDate() != null, DdOblistSupple::getModifyDate, bo.getModifyDate());
        lqw.eq(StringUtils.isNotBlank(bo.getModifyUser()), DdOblistSupple::getModifyUser, bo.getModifyUser());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceId()), DdOblistSupple::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getExtend1()), DdOblistSupple::getExtend1, bo.getExtend1());
        lqw.eq(StringUtils.isNotBlank(bo.getExtend2()), DdOblistSupple::getExtend2, bo.getExtend2());
        lqw.eq(StringUtils.isNotBlank(bo.getExtend3()), DdOblistSupple::getExtend3, bo.getExtend3());
        lqw.eq(StringUtils.isNotBlank(bo.getExtend4()), DdOblistSupple::getExtend4, bo.getExtend4());
        lqw.eq(StringUtils.isNotBlank(bo.getExtend5()), DdOblistSupple::getExtend5, bo.getExtend5());
        lqw.eq(StringUtils.isNotBlank(bo.getExtend6()), DdOblistSupple::getExtend6, bo.getExtend6());
        lqw.eq(StringUtils.isNotBlank(bo.getExtend7()), DdOblistSupple::getExtend7, bo.getExtend7());
        lqw.eq(StringUtils.isNotBlank(bo.getExtend8()), DdOblistSupple::getExtend8, bo.getExtend8());
        lqw.eq(StringUtils.isNotBlank(bo.getExtend9()), DdOblistSupple::getExtend9, bo.getExtend9());
        lqw.eq(StringUtils.isNotBlank(bo.getExtend10()), DdOblistSupple::getExtend10, bo.getExtend10());
        lqw.eq(bo.getRowNm() != null, DdOblistSupple::getRowNm, bo.getRowNm());
        lqw.eq(StringUtils.isNotBlank(bo.getExtend11()), DdOblistSupple::getExtend11, bo.getExtend11());
        lqw.eq(StringUtils.isNotBlank(bo.getExtend12()), DdOblistSupple::getExtend12, bo.getExtend12());
        lqw.eq(StringUtils.isNotBlank(bo.getExtend13()), DdOblistSupple::getExtend13, bo.getExtend13());
        lqw.eq(StringUtils.isNotBlank(bo.getExtend14()), DdOblistSupple::getExtend14, bo.getExtend14());
        lqw.eq(StringUtils.isNotBlank(bo.getExtend15()), DdOblistSupple::getExtend15, bo.getExtend15());
        lqw.eq(bo.getSuppleSource() != null, DdOblistSupple::getSuppleSource, bo.getSuppleSource());
        lqw.eq(bo.getSuppleStatus() != null, DdOblistSupple::getSuppleStatus, bo.getSuppleStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getCorrectionType()), DdOblistSupple::getCorrectionType, bo.getCorrectionType());
        lqw.eq(StringUtils.isNotBlank(bo.getCorrectionDesc()), DdOblistSupple::getCorrectionDesc, bo.getCorrectionDesc());
        lqw.eq(bo.getSuppleId() != null, DdOblistSupple::getSuppleId, bo.getSuppleId());
        lqw.eq(StringUtils.isNotBlank(bo.getSuppleDt()), DdOblistSupple::getSuppleDt, bo.getSuppleDt());
        lqw.eq(bo.getApplyId() != null, DdOblistSupple::getApplyId, bo.getApplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyDt()), DdOblistSupple::getApplyDt, bo.getApplyDt());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyComments()), DdOblistSupple::getApplyComments, bo.getApplyComments());
        return lqw;
    }

    /**
     * 新增关注名单主数据
     */
    @Override
    public Boolean insertByBo(DdOblistSuppleBo bo) {
        DdOblistSupple add = BeanUtil.toBean(bo, DdOblistSupple.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setObjId(add.getObjId());
        }
        return flag;
    }

    /**
     * 修改关注名单主数据
     */
    @Override
    public Boolean updateByBo(DdOblistSuppleBo bo) {
        //根据id查询原始数据
        DdOblistSuppleVo orgData = baseMapper.selectVoById(bo.getObjId());
        //转换数据
        DdOblistSupple update = BeanUtil.toBean(bo, DdOblistSupple.class);
        if(2 == update.getSuppleStatus() || 3 == update.getSuppleStatus()){
            update.setSuppleId(LoginHelper.getUserId());
            update.setSuppleDt(DateUtils.getNowDate());
        }
        if(4 == update.getSuppleStatus() || 9 == update.getSuppleStatus()){
            update.setApplyId(LoginHelper.getUserId());
            update.setApplyDt(DateUtils.getNowDate());
        }
        boolean resultFlag = baseMapper.updateById(update) > 0;
        if (resultFlag) {
            if(4 == update.getSuppleStatus() || 9 == update.getSuppleStatus()){
                Sm10EditCheckLog sm10EditCheckLog = new Sm10EditCheckLog();
                sm10EditCheckLog.setTbName("dd_oblist_supple");
                sm10EditCheckLog.setRecordId(update.getObjId());
                sm10EditCheckLog.setSuppleStatus(bo.getSuppleStatus());
                sm10EditCheckLog.setApplyComments(bo.getApplyComments());
                sm10EditCheckLog.setCreateUser(LoginHelper.getUserId());
                sm10EditCheckLog.setCreateDt(DateUtils.getNowDate());
                sm10EditCheckLog.setModifyUser(LoginHelper.getUserId());
                sm10EditCheckLog.setModifyDt(DateUtils.getNowDate());
                sm10EditCheckLogMapper.insert(sm10EditCheckLog);
            }else{
                //进行暂存后数据库操作
                Sm10EditTableLog sm10EditTableLog = new Sm10EditTableLog();
                sm10EditTableLog.setTableNo("dd_oblist_supple");
                sm10EditTableLog.setRelationCln("obj_id");
                sm10EditTableLog.setPkValue(update.getObjId());
                sm10EditTableLog.setModifiedBefore(JSONUtil.toJsonStr(orgData));
                sm10EditTableLog.setModifiedAfter(JSONUtil.toJsonStr(update));
                sm10EditTableLog.setCreateUser(LoginHelper.getUserId());
                sm10EditTableLog.setCreateDt(DateUtils.getNowDate());

                sm10EditTableLogMapper.insert(sm10EditTableLog);

                Sm10EditCheckLog sm10EditCheckLog = new Sm10EditCheckLog();
                sm10EditCheckLog.setTbName("dd_oblist_supple");
                sm10EditCheckLog.setRecordId(update.getObjId());
                sm10EditCheckLog.setSuppleStatus(bo.getSuppleStatus());
                if(2 == update.getSuppleStatus()){
                    sm10EditCheckLog.setApplyComments("补录暂存");
                }else {
                    sm10EditCheckLog.setApplyComments("提交审核");
                }
                sm10EditCheckLog.setCreateUser(LoginHelper.getUserId());
                sm10EditCheckLog.setCreateDt(DateUtils.getNowDate());
                sm10EditCheckLog.setModifyUser(LoginHelper.getUserId());
                sm10EditCheckLog.setModifyDt(DateUtils.getNowDate());
                sm10EditCheckLogMapper.insert(sm10EditCheckLog);
            }
        }
        //查询是否是专题补录
        LambdaQueryWrapper<Sm10SpecialSuppleMap> lqw = Wrappers.lambdaQuery();
        lqw.eq(true, Sm10SpecialSuppleMap::getObjId, update.getObjId());
        List<Sm10SpecialSuppleMapVo> sm10SpecialSuppleMapVos = sm10SpecialSuppleMapMapper.selectVoList(lqw);
        if (sm10SpecialSuppleMapVos != null && !sm10SpecialSuppleMapVos.isEmpty()) {
            for (Sm10SpecialSuppleMapVo vo : sm10SpecialSuppleMapVos) {
                if(vo.getObjId().equals(update.getObjId())){
                    Sm10SpecialSuppleMap sm10SpecialSuppleMap = new Sm10SpecialSuppleMap();
                    sm10SpecialSuppleMap.setId(vo.getId());
                    sm10SpecialSuppleMap.setSuppleStatus(update.getSuppleStatus());
                    sm10SpecialSuppleMap.setSuppleDt(DateUtils.getNowDate());
                    sm10SpecialSuppleMap.setSuppleId(update.getSuppleId());
                    sm10SpecialSuppleMapMapper.updateById(sm10SpecialSuppleMap);
                }
            }
        }
        return resultFlag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(DdOblistSupple entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除关注名单主数据
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 检查是否在补录中
     * 并且更新补录人
     * @param objId
     * @return
     */
    @Override
    public Boolean checkSuppleing(String objId) {
        DdOblistSuppleVo ddOblistSuppleVo = baseMapper.selectVoOne(
            new LambdaQueryWrapper<DdOblistSupple>()
                .eq(DdOblistSupple::getObjId, objId)
                .and(wrapper -> wrapper.isNull(DdOblistSupple::getSuppleId)
                    .or()
                    .eq(DdOblistSupple::getSuppleId, LoginHelper.getUserId()))
        );
        if(ddOblistSuppleVo != null){
            DdOblistSupple update = new DdOblistSupple();
            update.setObjId(objId);
            update.setSuppleId(LoginHelper.getUserId());
            baseMapper.updateById(update);
        }
        return ddOblistSuppleVo == null;
    }

    @Override
    public Boolean openSupple(String objId) {
        DdOblistSuppleVo ddOblistSuppleVo = baseMapper.selectVoById(objId);
        if(ddOblistSuppleVo == null){
            return false;
        }
        UpdateWrapper<DdOblistSupple> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("obj_id", objId)
            .set("supple_id", null); // 使用 set 方法并设置为 null

        return baseMapper.update(null, updateWrapper) > 0;
    }
}
