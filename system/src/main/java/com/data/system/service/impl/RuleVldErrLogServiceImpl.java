package com.data.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.data.common.core.page.TableDataInfo;
import com.data.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.data.common.utils.StringUtils;
import com.data.system.domain.bo.RuleVldDataBo;
import com.data.system.domain.vo.RuleVldDataVo;
import com.data.system.service.IRuleVldDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.data.system.domain.bo.RuleVldErrLogBo;
import com.data.system.domain.vo.RuleVldErrLogVo;
import com.data.system.domain.RuleVldErrLog;
import com.data.system.mapper.RuleVldErrLogMapper;
import com.data.system.service.IRuleVldErrLogService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 规则校验不通过日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-10
 */
@RequiredArgsConstructor
@Service
public class RuleVldErrLogServiceImpl implements IRuleVldErrLogService {

    private final RuleVldErrLogMapper baseMapper;

    private final IRuleVldDataService ruleVldDataService;

    /**
     * 查询规则校验不通过日志
     */
    @Override
    public RuleVldErrLogVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询规则校验不通过日志列表
     */
    @Override
    public TableDataInfo<RuleVldErrLogVo> queryPageList(RuleVldErrLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<RuleVldErrLog> lqw = buildQueryWrapper(bo);
        Page<RuleVldErrLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<RuleVldDataVo> ruleVldDataVos = ruleVldDataService.queryList(new RuleVldDataBo()); //查询当前规则编码列表
        Map<String,Object> map = this.listRuleVldDataVostoMap(ruleVldDataVos); //将规则校验数据列表转换为map
        for(RuleVldErrLogVo ruleVldErrLogVo : result.getRecords()) {
            ruleVldErrLogVo.setRuleCode(ruleVldErrLogVo.getRuleCode() + "-" + map.get(ruleVldErrLogVo.getRuleCode())); //将规则编码转换为规则名称
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询规则校验不通过日志列表
     */
    @Override
    public List<RuleVldErrLogVo> queryList(RuleVldErrLogBo bo) {
        LambdaQueryWrapper<RuleVldErrLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<RuleVldErrLog> buildQueryWrapper(RuleVldErrLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<RuleVldErrLog> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getSchemaName()), RuleVldErrLog::getSchemaName, bo.getSchemaName());
        lqw.like(StringUtils.isNotBlank(bo.getTableName()), RuleVldErrLog::getTableName, bo.getTableName());
        lqw.eq(StringUtils.isNotBlank(bo.getVldPk()), RuleVldErrLog::getVldPk, bo.getVldPk());
        lqw.like(StringUtils.isNotBlank(bo.getFieldName()), RuleVldErrLog::getFieldName, bo.getFieldName());
        lqw.eq(StringUtils.isNotBlank(bo.getFieldVal()), RuleVldErrLog::getFieldVal, bo.getFieldVal());
        lqw.eq(StringUtils.isNotBlank(bo.getRuleCode()), RuleVldErrLog::getRuleCode, bo.getRuleCode());
        if(params.get("beginCreateDt") != null && params.get("endCreateDt") != null){
            lqw.between(true,RuleVldErrLog::getCreateTime, params.get("beginCreateDt"), params.get("endCreateDt"));
        }
        lqw.orderByDesc(RuleVldErrLog::getCreateTime);
        return lqw;
    }

    /**
     * 新增规则校验不通过日志
     */
    @Override
    public Boolean insertByBo(RuleVldErrLogBo bo) {
        RuleVldErrLog add = BeanUtil.toBean(bo, RuleVldErrLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改规则校验不通过日志
     */
    @Override
    public Boolean updateByBo(RuleVldErrLogBo bo) {
        RuleVldErrLog update = BeanUtil.toBean(bo, RuleVldErrLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(RuleVldErrLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除规则校验不通过日志
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /*
     * 将规则校验数据列表转换为map
     * @param ruleVldDataVoList 规则校验数据列表
     * @return 规则校验数据map
     */
    public Map<String,Object> listRuleVldDataVostoMap(List<RuleVldDataVo> ruleVldDataVoList){
        Map<String,Object> map = new HashMap<>();
        for(RuleVldDataVo ruleVldDataVo : ruleVldDataVoList){
            map.put(ruleVldDataVo.getRuleCode(),ruleVldDataVo.getRuleName());
        }
        return map;
    }
}
