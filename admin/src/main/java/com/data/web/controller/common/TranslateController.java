package com.data.web.controller.common;

import com.data.common.annotation.Log;
import com.data.common.core.controller.BaseController;
import com.data.common.core.domain.PageQuery;
import com.data.common.core.domain.R;
import com.data.common.core.page.TableDataInfo;
import com.data.common.enums.BusinessType;
import com.data.common.utils.TranslateUtils;
import com.data.system.domain.bo.H04MidOpenSanPepMatchBo;
import com.data.system.domain.bo.H04OpenSanPepKeyIndexBo;
import com.data.system.domain.vo.H04MidOpenSanPepMatchVo;
import com.data.system.domain.vo.H04OpenSanPepKeyIndexVo;
import com.data.system.service.IH04OpenSanPepKeyIndexService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 翻译
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/translate")
public class TranslateController extends BaseController {

    /**
     * 翻译
     * @param content
     * @return
     */
    @GetMapping("/free")
    public R<String> free(@RequestParam String content) {
        String result = TranslateUtils.translateEnglishToChinese(content, "hunyuan-lite");
        return R.ok("翻译成功", result);
    }

    /**
     * 翻译
     * @param content
     * @return
     */
    @GetMapping("/vip")
    public R<String> vip(@RequestParam String content) {
        String result = TranslateUtils.translateEnglishToChinese(content, "Doubao-pro-32k");
        return R.ok("翻译成功", result);
    }
}
