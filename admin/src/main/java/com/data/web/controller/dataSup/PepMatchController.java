package com.data.web.controller.dataSup;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.data.common.annotation.Log;
import com.data.common.constant.UserConstants;
import com.data.common.core.controller.BaseController;
import com.data.common.core.domain.PageQuery;
import com.data.common.core.domain.R;
import com.data.common.core.page.TableDataInfo;
import com.data.common.enums.BusinessType;
import com.data.common.utils.TranslateUtils;
import com.data.common.utils.poi.ExcelUtil;
import com.data.system.domain.SysPost;
import com.data.system.domain.bo.H04MidOpenSanPepMatchBo;
import com.data.system.domain.bo.H04OpenSanPepKeyIndexBo;
import com.data.system.domain.vo.H04MidOpenSanPepMatchVo;
import com.data.system.domain.vo.H04OpenSanPepKeyIndexVo;
import com.data.system.service.IH04OpenSanPepKeyIndexService;
import com.data.system.service.ISysPostService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 数据补录-政要匹配认定
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/dataSup/pepMatch")
public class PepMatchController extends BaseController {

    private final IH04OpenSanPepKeyIndexService h04OpenSanPepKeyIndexService;

    /**
     * 列表
     */
    @GetMapping("/list")
    public TableDataInfo<H04OpenSanPepKeyIndexVo> list(H04OpenSanPepKeyIndexBo bo, PageQuery pageQuery) {
        return h04OpenSanPepKeyIndexService.queryPageList(bo, pageQuery);
    }

    /**
     * 比对列表
     */
    @GetMapping("/comList/{objId}")
    public R<List<H04MidOpenSanPepMatchVo>> comList(@PathVariable String objId) {
        return R.ok(h04OpenSanPepKeyIndexService.comList(objId));
    }

    /**
     * 提交
     * @param bo
     * @return
     */
    @Log(title = "政要匹配认定提交", businessType = BusinessType.UPDATE)
    @PostMapping("/submit")
    public R<Void> submit(@RequestBody H04MidOpenSanPepMatchBo bo) {
        return toAjax(h04OpenSanPepKeyIndexService.submit(bo));
    }

    /**
     * 翻译
     * @param content
     * @return
     */
    @GetMapping("/translate")
    public R<String> translate(@RequestParam String content) {
        return R.ok(TranslateUtils.translateEnglishToChinese(content, "hunyuan-lite"));
    }
}
