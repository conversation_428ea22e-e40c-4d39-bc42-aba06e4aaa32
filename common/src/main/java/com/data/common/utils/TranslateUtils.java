package com.data.common.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.data.common.core.domain.dto.TranslateApiRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 翻译工具类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TranslateUtils {

    private static String url;

    @Value("${translate.url}")
    public void setUrl(String url) {
        TranslateUtils.url = url;
    }

    /**
     * 调用翻译API进行内容翻译
     *
     * @param content 待翻译内容
     * @param chatType 聊天类型 免费模型: hunyuan-lite 收费模型: Doubao-pro-32k
     * @return 翻译结果，失败返回null
     */
    public static String translateContent(String content, String chatType) {
        return translateContent(content, chatType, "English", "Chinese");
    }

    /**
     * 调用翻译API进行内容翻译
     *
     * @param content 待翻译内容
     * @param chatType 聊天类型
     * @param originLanguage 源语言
     * @param translateLanguage 目标语言
     * @return 翻译结果，失败返回null
     */
    public static String translateContent(String content, String chatType,
                                        String originLanguage, String translateLanguage) {
        try {
            // 构建翻译请求
            TranslateApiRequest translateApiRequest = new TranslateApiRequest();
            translateApiRequest.setChatType(chatType);
            translateApiRequest.setContent(content);
            translateApiRequest.setOriginLanguage(originLanguage);
            translateApiRequest.setTranslateLanguage(translateLanguage);

            // 调用翻译接口
            String result = HttpUtils.postJson(url, JSONUtil.toJsonStr(translateApiRequest));

            // 检查返回结果
            if (StrUtil.isBlank(result)) {
                log.error("调用翻译接口失败，返回结果为空");
                return null;
            }

            // 解析返回结果
            if (!JSONUtil.parseObj(result).get("code").equals("0")) {
                log.error("调用翻译接口失败：{}", result);
                return null;
            }

            return (String) JSONUtil.parseObj(result).get("data");

        } catch (Exception e) {
            log.error("调用翻译API异常", e);
            return null;
        }
    }

    /**
     * 调用翻译API进行内容翻译（使用默认英译中）
     *
     * @param content 待翻译内容
     * @param chatType 聊天类型
     * @return 翻译结果，失败返回null
     */
    public static String translateEnglishToChinese(String content, String chatType) {
        return translateContent(content, chatType, "English", "Chinese");
    }

    /**
     * 调用翻译API进行内容翻译（使用默认中译英）
     *
     * @param content 待翻译内容
     * @param chatType 聊天类型
     * @return 翻译结果，失败返回null
     */
    public static String translateChineseToEnglish(String content, String chatType) {
        return translateContent(content, chatType, "Chinese", "English");
    }
}
