package com.data.job.service.translate;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.data.common.core.domain.dto.TranslateApiRequest;
import com.data.common.utils.HttpUtils;
import com.data.common.utils.StringUtils;
import com.data.common.utils.TranslateUtils;
import com.data.job.domain.dto.TcDetailOblistDto;
import com.data.job.mapper.TranslateMapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * TC_DETAIL_OBLIST翻译任务
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class DetailTranslateService {
    // 定义批处理大小常量
    private static final int BATCH_SIZE = 20;

    private final TranslateMapper translateMapper;

    private final ThreadPoolTaskExecutor translateTaskExecutor;

    /**
     * TC_DETAIL_OBLIST翻译任务
     */
    @XxlJob("detailTranslateJobHandler")
    public void detailTranslateJobHandler() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        if(StringUtils.isBlank(jobParam)){
            XxlJobHelper.handleFail("参数为空，任务结束");
        }

        XxlJobHelper.log("TC_DETAIL_OBLIST翻译任务定时任务开始");
        int totalProcessed = 0;
        int successCount = 0;
        int failCount = 0;
        try {
            // 获取待翻译的总数量
            int totalCount = translateMapper.detailCount();
            XxlJobHelper.log("TC_DETAIL_OBLIST待翻译数据总量: " + totalCount);

            if (totalCount <= 0) {
                XxlJobHelper.log("没有需要翻译的数据，任务结束");
                return;
            }

            while (true) {
                // 每次获取未翻译的数据
                List<TcDetailOblistDto> dataList = translateMapper.selectDeatilList(BATCH_SIZE);

                if (dataList == null || dataList.isEmpty()) {
                    break;
                }

                // 使用线程池并行处理翻译任务
                List<CompletableFuture<TcDetailOblistDto>> futures = new ArrayList<>();
                for (TcDetailOblistDto data : dataList) {
                    CompletableFuture<TcDetailOblistDto> future = CompletableFuture.supplyAsync(() -> {
                        return processTranslation(data, jobParam);
                    }, translateTaskExecutor);
                    futures.add(future);
                }

                // 等待所有翻译任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(5, TimeUnit.MINUTES);

                // 收集翻译结果
                List<TcDetailOblistDto> batchResults = new ArrayList<>();
                for (CompletableFuture<TcDetailOblistDto> future : futures) {
                    TcDetailOblistDto result = future.get();
                    if (result != null) {
                        batchResults.add(result);
                        if (StrUtil.isNotBlank(result.getIsTranslated())) {
                            if(result.getIsTranslated().equals("1")){
                                successCount++;
                            }else {
                                failCount++;
                            }
                        }
                    }
                }

                // 批量更新数据库
                if (!batchResults.isEmpty()) {
                    translateMapper.batchUpdateDetail(batchResults);
                }

                totalProcessed += dataList.size();
                // 每处理1000条记录打印一次进度日志
                if (totalProcessed % 1000 == 0) {
                    XxlJobHelper.log("已处理: {}/{}, 成功: {}, 失败: {}",
                        totalProcessed, totalCount, successCount, failCount);
                }
            }

            XxlJobHelper.handleSuccess(String.format("TC_DETAIL_OBLIST翻译定时任务成功结束，总处理: %d, 成功: %d, 失败: %d",
                totalProcessed, successCount, failCount));
        } catch (Exception e) {
            log.error("TC_DETAIL_OBLIST翻译定时任务执行过程中出现异常", e);
            XxlJobHelper.handleFail("TC_DETAIL_OBLIST翻译定时任务执行失败: " + e.getMessage()); // 使用handleFail记录失败
        }
    }

    private TcDetailOblistDto processTranslation(TcDetailOblistDto data, String jobParam) {
        String id = data.getId();
        try {
            TcDetailOblistDto result = new TcDetailOblistDto();
            result.setId(data.getId());

            result.setItemDesCn(StringUtils.isNotBlank(data.getItemDes()) ? translateContent(data.getItemDes(), jobParam) : null);
            result.setItemRemarkCn(StringUtils.isNotBlank(data.getItemRemark()) ? translateContent(data.getItemRemark(), jobParam) : null);
            result.setGeneralInfoCn(StringUtils.isNotBlank(data.getGeneralInfo()) ? translateContent(data.getGeneralInfo(), jobParam) : null);
            result.setRestrictedDesCn(StringUtils.isNotBlank(data.getRestrictedDes()) ? translateContent(data.getRestrictedDes(), jobParam) : null);
            result.setIsTranslated("1");
            return result;
        } catch (Exception e) {
            log.error("处理数据失败，objId={}", id, e);
            TcDetailOblistDto result = new TcDetailOblistDto();
            result.setIsTranslated("2");
            result.setId(id);
            return result;
        }
    }

    /**
     * 调用翻译API进行内容翻译
     */
    private String translateContent(String content, String jobParam) {
        return TranslateUtils.translateContent(content, jobParam);
    }
}
